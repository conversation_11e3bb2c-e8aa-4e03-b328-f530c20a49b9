{"name": "social-media-blocking-automation", "version": "1.0.0", "description": "Real web automation for social media blocking using Puppeteer", "main": "puppeteer_reporter.js", "scripts": {"start": "node puppeteer_reporter.js", "test": "node test_puppeteer.js", "install-deps": "npm install"}, "dependencies": {"puppeteer": "^21.0.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-extra-plugin-adblocker": "^2.13.6", "fs-extra": "^11.1.1", "path": "^0.12.7", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["puppeteer", "automation", "social-media", "instagram", "facebook", "reporting"], "author": "Social Media Blocking Module", "license": "MIT", "engines": {"node": ">=16.0.0"}}