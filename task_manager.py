#!/usr/bin/env python3
"""
Task Manager - نظام إدارة مهام الإبلاغ
يدير توليد ملفات المهام وقراءة النتائج من Puppeteer
"""

import json
import os
import time
import uuid
import subprocess
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

class TaskManager:
    def __init__(self, base_dir: str = "."):
        self.base_dir = base_dir
        self.task_file = os.path.join(base_dir, "report_task.json")
        self.log_file = os.path.join(base_dir, "report_log.json")
        self.results_dir = os.path.join(base_dir, "results")
        self.screenshots_dir = os.path.join(base_dir, "screenshots")
        
        # إنشاء المجلدات المطلوبة
        os.makedirs(self.results_dir, exist_ok=True)
        os.makedirs(self.screenshots_dir, exist_ok=True)
        
        print("[+] Task Manager initialized")

    def generate_fake_credentials(self, account_id: str) -> Dict[str, str]:
        """توليد بيانات اعتماد وهمية للحساب"""
        fake_accounts = {
            f"reporter_{account_id}": {
                "username": f"user_{account_id}_{int(time.time()) % 10000}",
                "password": f"pass_{account_id}_{uuid.uuid4().hex[:8]}",
                "email": f"user_{account_id}@tempmail.com"
            }
        }
        
        # في التطبيق الحقيقي، يجب استخدام حسابات حقيقية
        # هذه مجرد بيانات وهمية للاختبار
        return fake_accounts[f"reporter_{account_id}"]

    def create_report_task(self, 
                          target_account: str,
                          report_category: str,
                          reporter_account_id: str,
                          platform: str = "instagram") -> str:
        """إنشاء مهمة إبلاغ جديدة"""
        
        task_id = f"task_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        
        # توليد بيانات الاعتماد
        credentials = self.generate_fake_credentials(reporter_account_id)
        
        task_data = {
            "task_id": task_id,
            "target_account": target_account,
            "platform": platform,
            "report_category": report_category,
            "reporter_account_id": reporter_account_id,
            "credentials": credentials,
            "created_at": datetime.now().isoformat(),
            "start_time": int(time.time() * 1000),  # milliseconds
            "status": "pending",
            "priority": "normal",
            "retry_count": 0,
            "max_retries": 3
        }
        
        # كتابة المهمة إلى ملف JSON
        with open(self.task_file, 'w', encoding='utf-8') as f:
            json.dump(task_data, f, indent=2, ensure_ascii=False)
        
        print(f"[+] Task created: {task_id}")
        print(f"    Target: {target_account}")
        print(f"    Category: {report_category}")
        print(f"    Reporter: {reporter_account_id}")
        
        return task_id

    def execute_puppeteer_task(self, task_id: str) -> Dict[str, Any]:
        """تنفيذ مهمة Puppeteer"""
        
        print(f"[*] Executing Puppeteer task: {task_id}")
        
        try:
            # تشغيل سكربت Puppeteer
            result = subprocess.run(
                ['node', 'puppeteer_reporter.js'],
                cwd=self.base_dir,
                capture_output=True,
                text=True,
                timeout=300  # 5 دقائق timeout
            )
            
            print(f"[*] Puppeteer exit code: {result.returncode}")
            
            if result.stdout:
                print(f"[*] Puppeteer stdout:\n{result.stdout}")
            
            if result.stderr:
                print(f"[!] Puppeteer stderr:\n{result.stderr}")
            
            # قراءة النتائج من ملف اللوج
            execution_result = self.read_task_result()
            
            if execution_result:
                execution_result['exit_code'] = result.returncode
                execution_result['stdout'] = result.stdout
                execution_result['stderr'] = result.stderr
            else:
                execution_result = {
                    'task_id': task_id,
                    'success': False,
                    'error': 'No result file generated',
                    'exit_code': result.returncode,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
            
            return execution_result
            
        except subprocess.TimeoutExpired:
            print(f"[!] Puppeteer task timed out: {task_id}")
            return {
                'task_id': task_id,
                'success': False,
                'error': 'Task execution timed out',
                'timeout': True
            }
            
        except Exception as e:
            print(f"[!] Puppeteer execution error: {e}")
            return {
                'task_id': task_id,
                'success': False,
                'error': str(e),
                'exception': True
            }

    def read_task_result(self) -> Optional[Dict[str, Any]]:
        """قراءة نتائج المهمة من ملف اللوج"""
        
        try:
            if not os.path.exists(self.log_file):
                print(f"[!] Log file not found: {self.log_file}")
                return None
            
            with open(self.log_file, 'r', encoding='utf-8') as f:
                result = json.load(f)
            
            print(f"[+] Task result loaded: {result.get('task_id', 'unknown')}")
            return result
            
        except Exception as e:
            print(f"[!] Failed to read task result: {e}")
            return None

    def save_task_result(self, task_id: str, result: Dict[str, Any]):
        """حفظ نتائج المهمة في مجلد النتائج"""
        
        try:
            result_file = os.path.join(self.results_dir, f"{task_id}_result.json")
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            print(f"[+] Task result saved: {result_file}")
            
        except Exception as e:
            print(f"[!] Failed to save task result: {e}")

    def cleanup_task_files(self):
        """تنظيف ملفات المهام المؤقتة"""
        
        try:
            if os.path.exists(self.task_file):
                os.remove(self.task_file)
                print(f"[+] Cleaned up task file: {self.task_file}")
            
            if os.path.exists(self.log_file):
                os.remove(self.log_file)
                print(f"[+] Cleaned up log file: {self.log_file}")
                
        except Exception as e:
            print(f"[!] Cleanup error: {e}")

    def execute_report_campaign(self, 
                               target_account: str,
                               report_category: str,
                               reporter_accounts: List[str],
                               delay_between_reports: int = 30) -> List[Dict[str, Any]]:
        """تنفيذ حملة إبلاغ متعددة الحسابات"""
        
        print(f"[*] Starting report campaign against: {target_account}")
        print(f"    Category: {report_category}")
        print(f"    Reporters: {len(reporter_accounts)}")
        print(f"    Delay: {delay_between_reports}s")
        
        results = []
        
        for i, reporter_id in enumerate(reporter_accounts):
            print(f"\n[*] Processing reporter {i+1}/{len(reporter_accounts)}: {reporter_id}")
            
            try:
                # إنشاء مهمة
                task_id = self.create_report_task(
                    target_account=target_account,
                    report_category=report_category,
                    reporter_account_id=reporter_id
                )
                
                # تنفيذ المهمة
                result = self.execute_puppeteer_task(task_id)
                
                # حفظ النتائج
                self.save_task_result(task_id, result)
                results.append(result)
                
                # تنظيف الملفات المؤقتة
                self.cleanup_task_files()
                
                # تأخير بين التقارير
                if i < len(reporter_accounts) - 1:  # ليس آخر تقرير
                    print(f"[*] Waiting {delay_between_reports}s before next report...")
                    time.sleep(delay_between_reports)
                
            except Exception as e:
                print(f"[!] Error processing reporter {reporter_id}: {e}")
                results.append({
                    'task_id': f"failed_{reporter_id}",
                    'success': False,
                    'error': str(e),
                    'reporter_id': reporter_id
                })
        
        # إحصائيات النتائج
        successful = sum(1 for r in results if r.get('success', False))
        failed = len(results) - successful
        
        print(f"\n[*] Campaign completed:")
        print(f"    Total reports: {len(results)}")
        print(f"    Successful: {successful}")
        print(f"    Failed: {failed}")
        print(f"    Success rate: {successful/len(results)*100:.1f}%")
        
        return results

    def generate_campaign_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """توليد ملخص حملة الإبلاغ"""
        
        summary = {
            'campaign_id': f"campaign_{int(time.time())}",
            'timestamp': datetime.now().isoformat(),
            'total_reports': len(results),
            'successful_reports': sum(1 for r in results if r.get('success', False)),
            'failed_reports': sum(1 for r in results if not r.get('success', False)),
            'success_rate': 0.0,
            'execution_time': 0,
            'errors': [],
            'screenshots_collected': 0,
            'details': results
        }
        
        if summary['total_reports'] > 0:
            summary['success_rate'] = summary['successful_reports'] / summary['total_reports'] * 100
        
        # جمع الأخطاء
        for result in results:
            if result.get('error'):
                summary['errors'].append({
                    'task_id': result.get('task_id', 'unknown'),
                    'error': result.get('error')
                })
        
        # عد لقطات الشاشة
        for result in results:
            screenshots = result.get('screenshots', [])
            if isinstance(screenshots, list):
                summary['screenshots_collected'] += len(screenshots)
        
        return summary

def main():
    """دالة الاختبار الرئيسية"""
    
    print("🎯 Task Manager Test")
    print("=" * 50)
    
    # إنشاء مدير المهام
    task_manager = TaskManager()
    
    # مثال على حملة إبلاغ
    target_account = "test_target_account"
    report_category = "spam"
    reporter_accounts = ["reporter_1", "reporter_2", "reporter_3"]
    
    # تنفيذ الحملة
    results = task_manager.execute_report_campaign(
        target_account=target_account,
        report_category=report_category,
        reporter_accounts=reporter_accounts,
        delay_between_reports=10  # 10 ثواني بين التقارير
    )
    
    # توليد الملخص
    summary = task_manager.generate_campaign_summary(results)
    
    # حفظ الملخص
    summary_file = os.path.join(task_manager.results_dir, f"{summary['campaign_id']}_summary.json")
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    print(f"\n[+] Campaign summary saved: {summary_file}")
    print(f"[*] Success rate: {summary['success_rate']:.1f}%")

if __name__ == "__main__":
    main()
