# Enhanced Social Media Blocking System Requirements
# متطلبات نظام حجب وسائل التواصل الاجتماعي المطور

# Core Dependencies - التبعيات الأساسية
scrapy>=2.5.0
requests>=2.25.0
numpy>=1.20.0

# Optional Dependencies - التبعيات الاختيارية
# For enhanced web scraping
selenium>=4.0.0
beautifulsoup4>=4.9.0
lxml>=4.6.0

# For data processing
pandas>=1.3.0
matplotlib>=3.3.0

# For async operations
aiohttp>=3.8.0
asyncio-throttle>=1.0.0

# For proxy support
requests[socks]>=2.25.0
pysocks>=1.7.0

# For image processing (screenshots)
Pillow>=8.0.0

# For database operations
sqlalchemy>=1.4.0

# For configuration management
pyyaml>=5.4.0
configparser>=5.0.0

# For logging and monitoring
colorlog>=6.0.0
psutil>=5.8.0

# For testing
pytest>=6.2.0
pytest-cov>=2.12.0
pytest-mock>=3.6.0

# Development tools
black>=21.0.0
flake8>=3.9.0
mypy>=0.812
