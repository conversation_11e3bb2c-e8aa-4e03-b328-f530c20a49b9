#!/usr/bin/env python3
"""
Account Monitor Spider - مراقب حالة الحسابات
يستخدم Scrapy للتحقق من حالة الحسابات بعد حملات الإبلاغ
"""

import scrapy
import json
import os
import time
from datetime import datetime
from urllib.parse import urljoin
import requests
from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings

class AccountMonitorSpider(scrapy.Spider):
    name = 'account_monitor'
    
    def __init__(self, accounts_file='accounts_to_monitor.json', *args, **kwargs):
        super(AccountMonitorSpider, self).__init__(*args, **kwargs)
        self.accounts_file = accounts_file
        self.status_log_file = 'status_log.json'
        self.results = []
        
        # قراءة قائمة الحسابات المراد مراقبتها
        self.accounts_to_monitor = self.load_accounts_list()
        
        # إعدادات المراقبة
        self.platforms = {
            'instagram': {
                'base_url': 'https://www.instagram.com/',
                'not_found_indicators': [
                    'Sorry, this page isn\'t available',
                    'User not found',
                    'This account is private',
                    'No posts yet'
                ],
                'suspended_indicators': [
                    'Account suspended',
                    'This account has been disabled',
                    'Account not available'
                ]
            },
            'facebook': {
                'base_url': 'https://www.facebook.com/',
                'not_found_indicators': [
                    'Content not found',
                    'Page not available',
                    'Profile not available'
                ],
                'suspended_indicators': [
                    'Account suspended',
                    'Profile disabled',
                    'Community standards'
                ]
            },
            'twitter': {
                'base_url': 'https://twitter.com/',
                'not_found_indicators': [
                    'This account doesn\'t exist',
                    'User not found',
                    '@username doesn\'t exist'
                ],
                'suspended_indicators': [
                    'Account suspended',
                    'This account has been suspended',
                    'Suspended account'
                ]
            }
        }

    def load_accounts_list(self):
        """تحميل قائمة الحسابات من ملف JSON"""
        try:
            if os.path.exists(self.accounts_file):
                with open(self.accounts_file, 'r', encoding='utf-8') as f:
                    accounts = json.load(f)
                self.logger.info(f"Loaded {len(accounts)} accounts to monitor")
                return accounts
            else:
                self.logger.warning(f"Accounts file {self.accounts_file} not found")
                return []
        except Exception as e:
            self.logger.error(f"Error loading accounts: {e}")
            return []

    def start_requests(self):
        """بدء طلبات المراقبة"""
        for account in self.accounts_to_monitor:
            platform = account.get('platform', 'instagram')
            username = account.get('username', '')
            
            if platform in self.platforms and username:
                url = self.build_profile_url(platform, username)
                
                yield scrapy.Request(
                    url=url,
                    callback=self.parse_account_status,
                    meta={
                        'account': account,
                        'platform': platform,
                        'username': username
                    },
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                    }
                )

    def build_profile_url(self, platform, username):
        """بناء رابط الملف الشخصي"""
        base_url = self.platforms[platform]['base_url']
        
        if platform == 'instagram':
            return urljoin(base_url, f"{username}/")
        elif platform == 'facebook':
            return urljoin(base_url, f"{username}")
        elif platform == 'twitter':
            return urljoin(base_url, f"{username}")
        
        return base_url

    def parse_account_status(self, response):
        """تحليل حالة الحساب"""
        account = response.meta['account']
        platform = response.meta['platform']
        username = response.meta['username']
        
        status_result = {
            'username': username,
            'platform': platform,
            'url': response.url,
            'status_code': response.status,
            'check_time': datetime.now().isoformat(),
            'account_status': 'unknown',
            'details': {},
            'campaign_id': account.get('campaign_id', ''),
            'target_account': account.get('target_account', username)
        }

        try:
            # تحليل محتوى الصفحة
            page_content = response.text.lower()
            
            # التحقق من حالة الحساب
            if response.status == 404:
                status_result['account_status'] = 'not_found'
                status_result['details']['reason'] = 'HTTP 404 - Page not found'
                
            elif response.status == 403:
                status_result['account_status'] = 'access_denied'
                status_result['details']['reason'] = 'HTTP 403 - Access denied'
                
            elif self.check_suspended_indicators(page_content, platform):
                status_result['account_status'] = 'suspended'
                status_result['details']['reason'] = 'Account appears to be suspended'
                
            elif self.check_not_found_indicators(page_content, platform):
                status_result['account_status'] = 'not_found'
                status_result['details']['reason'] = 'Account not found or deleted'
                
            elif self.check_private_account(page_content, platform):
                status_result['account_status'] = 'private'
                status_result['details']['reason'] = 'Account is private'
                
            elif response.status == 200:
                status_result['account_status'] = 'active'
                status_result['details']['reason'] = 'Account appears to be active'
                
                # جمع معلومات إضافية
                status_result['details'].update(
                    self.extract_account_info(response, platform)
                )
            
            # حفظ النتيجة
            self.results.append(status_result)
            
            self.logger.info(
                f"Checked {username} on {platform}: {status_result['account_status']}"
            )
            
        except Exception as e:
            status_result['account_status'] = 'error'
            status_result['details']['error'] = str(e)
            self.results.append(status_result)
            self.logger.error(f"Error checking {username}: {e}")

        return status_result

    def check_suspended_indicators(self, content, platform):
        """التحقق من مؤشرات تعليق الحساب"""
        indicators = self.platforms[platform]['suspended_indicators']
        return any(indicator.lower() in content for indicator in indicators)

    def check_not_found_indicators(self, content, platform):
        """التحقق من مؤشرات عدم وجود الحساب"""
        indicators = self.platforms[platform]['not_found_indicators']
        return any(indicator.lower() in content for indicator in indicators)

    def check_private_account(self, content, platform):
        """التحقق من الحسابات الخاصة"""
        private_indicators = [
            'this account is private',
            'private account',
            'protected tweets',
            'private profile'
        ]
        return any(indicator in content for indicator in private_indicators)

    def extract_account_info(self, response, platform):
        """استخراج معلومات إضافية عن الحساب"""
        info = {}
        
        try:
            if platform == 'instagram':
                # استخراج عدد المتابعين والمنشورات
                content = response.text
                
                # البحث عن البيانات الوصفية
                if 'followers' in content:
                    info['has_followers_info'] = True
                if 'posts' in content:
                    info['has_posts_info'] = True
                    
            elif platform == 'twitter':
                # استخراج معلومات تويتر
                if 'tweets' in response.text.lower():
                    info['has_tweets'] = True
                    
            elif platform == 'facebook':
                # استخراج معلومات فيسبوك
                if 'timeline' in response.text.lower():
                    info['has_timeline'] = True
                    
        except Exception as e:
            info['extraction_error'] = str(e)
            
        return info

    def closed(self, reason):
        """حفظ النتائج عند انتهاء العنكبوت"""
        try:
            # إنشاء ملخص النتائج
            summary = {
                'scan_time': datetime.now().isoformat(),
                'total_accounts': len(self.results),
                'status_summary': {},
                'results': self.results
            }
            
            # حساب إحصائيات الحالات
            for result in self.results:
                status = result['account_status']
                summary['status_summary'][status] = summary['status_summary'].get(status, 0) + 1
            
            # حفظ النتائج
            with open(self.status_log_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Monitoring completed. Results saved to {self.status_log_file}")
            self.logger.info(f"Status summary: {summary['status_summary']}")
            
        except Exception as e:
            self.logger.error(f"Error saving results: {e}")

class AccountMonitorManager:
    """مدير مراقبة الحسابات"""
    
    def __init__(self):
        self.accounts_file = 'accounts_to_monitor.json'
        self.status_log_file = 'status_log.json'
    
    def create_monitoring_list(self, campaign_results):
        """إنشاء قائمة مراقبة من نتائج الحملة"""
        accounts_to_monitor = []
        
        for result in campaign_results:
            if result.get('success', False):
                account_info = {
                    'username': result.get('target_account', ''),
                    'platform': result.get('platform', 'instagram'),
                    'campaign_id': result.get('campaign_id', ''),
                    'report_time': result.get('timestamp', datetime.now().isoformat()),
                    'target_account': result.get('target_account', '')
                }
                accounts_to_monitor.append(account_info)
        
        # حفظ القائمة
        with open(self.accounts_file, 'w', encoding='utf-8') as f:
            json.dump(accounts_to_monitor, f, indent=2, ensure_ascii=False)
        
        print(f"[+] Created monitoring list with {len(accounts_to_monitor)} accounts")
        return accounts_to_monitor
    
    def run_monitoring(self):
        """تشغيل مراقبة الحسابات"""
        print("[*] Starting account monitoring...")
        
        # إعدادات Scrapy
        settings = get_project_settings()
        settings.update({
            'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'ROBOTSTXT_OBEY': False,
            'DOWNLOAD_DELAY': 2,
            'RANDOMIZE_DOWNLOAD_DELAY': True,
            'CONCURRENT_REQUESTS': 1,
            'CONCURRENT_REQUESTS_PER_DOMAIN': 1,
            'LOG_LEVEL': 'INFO'
        })
        
        # تشغيل العنكبوت
        process = CrawlerProcess(settings)
        process.crawl(AccountMonitorSpider, accounts_file=self.accounts_file)
        process.start()
    
    def get_monitoring_results(self):
        """الحصول على نتائج المراقبة"""
        try:
            if os.path.exists(self.status_log_file):
                with open(self.status_log_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except Exception as e:
            print(f"[!] Error reading monitoring results: {e}")
            return None

def main():
    """دالة الاختبار الرئيسية"""
    print("🕷️ Account Monitor Test")
    print("=" * 50)
    
    # إنشاء مدير المراقبة
    monitor = AccountMonitorManager()
    
    # إنشاء قائمة اختبار
    test_accounts = [
        {
            'username': 'test_account_1',
            'platform': 'instagram',
            'campaign_id': 'test_campaign_1',
            'target_account': 'test_account_1'
        },
        {
            'username': 'test_account_2',
            'platform': 'instagram',
            'campaign_id': 'test_campaign_2',
            'target_account': 'test_account_2'
        }
    ]
    
    # حفظ قائمة الاختبار
    with open(monitor.accounts_file, 'w', encoding='utf-8') as f:
        json.dump(test_accounts, f, indent=2, ensure_ascii=False)
    
    print(f"[+] Created test monitoring list: {monitor.accounts_file}")
    
    # تشغيل المراقبة
    monitor.run_monitoring()
    
    # عرض النتائج
    results = monitor.get_monitoring_results()
    if results:
        print(f"\n[+] Monitoring completed:")
        print(f"    Total accounts: {results['total_accounts']}")
        print(f"    Status summary: {results['status_summary']}")

if __name__ == "__main__":
    main()
