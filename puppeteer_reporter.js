#!/usr/bin/env node

/**
 * Puppeteer Reporter - Real Instagram Reporting Automation
 * تطبيق حقيقي للإبلاغ على Instagram باستخدام Puppeteer
 */

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const AdblockerPlugin = require('puppeteer-extra-plugin-adblocker');
const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// إضافة الإضافات لتجنب الاكتشاف
puppeteer.use(StealthPlugin());
puppeteer.use(AdblockerPlugin({ blockTrackers: true }));

class InstagramReporter {
    constructor() {
        this.browser = null;
        this.page = null;
        this.isLoggedIn = false;
        this.taskFile = 'report_task.json';
        this.logFile = 'report_log.json';
        this.screenshotDir = 'screenshots';
        
        // إعدادات التأخير العشوائي
        this.delays = {
            short: [1000, 3000],    // 1-3 ثواني
            medium: [3000, 7000],   // 3-7 ثواني
            long: [5000, 12000]     // 5-12 ثانية
        };
        
        this.log('🚀 Instagram Reporter initialized');
    }

    /**
     * تسجيل الرسائل مع الوقت
     */
    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = {
            'info': '[ℹ️]',
            'success': '[✅]',
            'warning': '[⚠️]',
            'error': '[❌]',
            'debug': '[🔍]'
        }[type] || '[ℹ️]';
        
        console.log(`${timestamp} ${prefix} ${message}`);
    }

    /**
     * تأخير عشوائي لتجنب الاكتشاف
     */
    async randomDelay(type = 'medium') {
        const [min, max] = this.delays[type];
        const delay = Math.floor(Math.random() * (max - min + 1)) + min;
        this.log(`⏳ Waiting ${delay}ms...`, 'debug');
        await new Promise(resolve => setTimeout(resolve, delay));
    }

    /**
     * قراءة مهمة الإبلاغ من ملف JSON
     */
    async readReportTask() {
        try {
            if (!await fs.pathExists(this.taskFile)) {
                throw new Error(`Task file ${this.taskFile} not found`);
            }
            
            const taskData = await fs.readJson(this.taskFile);
            this.log(`📋 Task loaded: ${taskData.task_id}`, 'success');
            return taskData;
        } catch (error) {
            this.log(`Failed to read task file: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * كتابة نتائج الإبلاغ إلى ملف JSON
     */
    async writeReportLog(result) {
        try {
            const logData = {
                timestamp: new Date().toISOString(),
                task_id: result.task_id,
                success: result.success,
                details: result.details,
                screenshots: result.screenshots || [],
                error: result.error || null
            };
            
            await fs.writeJson(this.logFile, logData, { spaces: 2 });
            this.log(`📝 Results logged to ${this.logFile}`, 'success');
        } catch (error) {
            this.log(`Failed to write log: ${error.message}`, 'error');
        }
    }

    /**
     * التقاط لقطة شاشة
     */
    async takeScreenshot(name) {
        try {
            await fs.ensureDir(this.screenshotDir);
            const filename = `${name}_${Date.now()}.png`;
            const filepath = path.join(this.screenshotDir, filename);
            
            await this.page.screenshot({ 
                path: filepath, 
                fullPage: true 
            });
            
            this.log(`📸 Screenshot saved: ${filename}`, 'debug');
            return filename;
        } catch (error) {
            this.log(`Screenshot failed: ${error.message}`, 'warning');
            return null;
        }
    }

    /**
     * بدء تشغيل المتصفح
     */
    async launchBrowser() {
        try {
            this.log('🌐 Launching browser...');
            
            this.browser = await puppeteer.launch({
                headless: false, // إظهار المتصفح للمراقبة
                defaultViewport: null,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--window-size=1366,768'
                ]
            });
            
            this.page = await this.browser.newPage();
            
            // تعيين User Agent واقعي
            await this.page.setUserAgent(
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            );
            
            // تعيين إعدادات إضافية
            await this.page.setViewport({ width: 1366, height: 768 });
            await this.page.setDefaultTimeout(30000);
            
            this.log('✅ Browser launched successfully', 'success');
        } catch (error) {
            this.log(`Browser launch failed: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * تسجيل الدخول إلى Instagram
     */
    async loginToInstagram(credentials) {
        try {
            this.log('🔐 Logging into Instagram...');
            
            await this.page.goto('https://www.instagram.com/accounts/login/', {
                waitUntil: 'networkidle2'
            });
            
            await this.randomDelay('medium');
            await this.takeScreenshot('login_page');
            
            // انتظار ظهور حقول تسجيل الدخول
            await this.page.waitForSelector('input[name="username"]');
            await this.page.waitForSelector('input[name="password"]');
            
            // إدخال اسم المستخدم
            await this.page.type('input[name="username"]', credentials.username, {
                delay: Math.random() * 100 + 50
            });
            
            await this.randomDelay('short');
            
            // إدخال كلمة المرور
            await this.page.type('input[name="password"]', credentials.password, {
                delay: Math.random() * 100 + 50
            });
            
            await this.randomDelay('short');
            await this.takeScreenshot('login_filled');
            
            // النقر على زر تسجيل الدخول
            await this.page.click('button[type="submit"]');
            
            // انتظار تحميل الصفحة الرئيسية أو رسالة خطأ
            await this.page.waitForNavigation({ waitUntil: 'networkidle2' });
            await this.randomDelay('long');
            
            // التحقق من نجاح تسجيل الدخول
            const currentUrl = this.page.url();
            if (currentUrl.includes('/accounts/login/')) {
                throw new Error('Login failed - still on login page');
            }
            
            await this.takeScreenshot('login_success');
            this.isLoggedIn = true;
            this.log('✅ Successfully logged into Instagram', 'success');
            
        } catch (error) {
            await this.takeScreenshot('login_error');
            this.log(`Login failed: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * زيارة صفحة المستخدم المستهدف
     */
    async visitTargetProfile(targetAccount) {
        try {
            this.log(`👤 Visiting target profile: ${targetAccount}`);
            
            const profileUrl = `https://www.instagram.com/${targetAccount}/`;
            await this.page.goto(profileUrl, { waitUntil: 'networkidle2' });
            
            await this.randomDelay('medium');
            await this.takeScreenshot('target_profile');
            
            // التحقق من وجود الحساب
            const pageContent = await this.page.content();
            if (pageContent.includes('Sorry, this page isn\'t available') || 
                pageContent.includes('User not found')) {
                throw new Error(`Target account ${targetAccount} not found`);
            }
            
            this.log(`✅ Successfully loaded profile: ${targetAccount}`, 'success');
            
        } catch (error) {
            await this.takeScreenshot('profile_error');
            this.log(`Failed to visit profile: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * فتح قائمة الإبلاغ
     */
    async openReportMenu() {
        try {
            this.log('📋 Opening report menu...');
            
            // البحث عن زر الخيارات (ثلاث نقاط)
            const optionsButton = await this.page.$('button[aria-label="Options"]') ||
                                 await this.page.$('svg[aria-label="Options"]') ||
                                 await this.page.$('[data-testid="user-options-button"]');
            
            if (!optionsButton) {
                throw new Error('Options button not found');
            }
            
            await optionsButton.click();
            await this.randomDelay('short');
            await this.takeScreenshot('options_menu');
            
            // البحث عن خيار الإبلاغ
            await this.page.waitForSelector('button, a', { timeout: 5000 });
            
            const reportButton = await this.page.evaluateHandle(() => {
                const buttons = Array.from(document.querySelectorAll('button, a'));
                return buttons.find(btn => 
                    btn.textContent.includes('Report') ||
                    btn.textContent.includes('إبلاغ') ||
                    btn.textContent.includes('Signaler')
                );
            });
            
            if (!reportButton.asElement()) {
                throw new Error('Report button not found in menu');
            }
            
            await reportButton.asElement().click();
            await this.randomDelay('medium');
            await this.takeScreenshot('report_menu_opened');
            
            this.log('✅ Report menu opened successfully', 'success');
            
        } catch (error) {
            await this.takeScreenshot('report_menu_error');
            this.log(`Failed to open report menu: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * اختيار نوع البلاغ
     */
    async selectReportType(reportCategory) {
        try {
            this.log(`🎯 Selecting report type: ${reportCategory}`);
            
            await this.randomDelay('short');
            
            // خريطة أنواع البلاغات
            const reportTypes = {
                'spam': ['Spam', 'It\'s spam', 'إنه رسائل مزعجة'],
                'harassment': ['Harassment', 'Bullying or harassment', 'تنمر أو مضايقة'],
                'hate_speech': ['Hate speech', 'Hate speech or symbols', 'خطاب كراهية'],
                'impersonation': ['Impersonation', 'Pretending to be someone else', 'انتحال شخصية'],
                'inappropriate_content': ['Inappropriate', 'Nudity or sexual activity', 'محتوى غير لائق']
            };
            
            const searchTerms = reportTypes[reportCategory] || reportTypes['spam'];
            
            // البحث عن الخيار المناسب
            let selectedOption = null;
            for (const term of searchTerms) {
                selectedOption = await this.page.evaluateHandle((searchTerm) => {
                    const buttons = Array.from(document.querySelectorAll('button, div[role="button"]'));
                    return buttons.find(btn => 
                        btn.textContent.includes(searchTerm)
                    );
                }, term);
                
                if (selectedOption.asElement()) {
                    break;
                }
            }
            
            if (!selectedOption.asElement()) {
                throw new Error(`Report type ${reportCategory} not found`);
            }
            
            await selectedOption.asElement().click();
            await this.randomDelay('medium');
            await this.takeScreenshot('report_type_selected');
            
            this.log(`✅ Report type selected: ${reportCategory}`, 'success');
            
        } catch (error) {
            await this.takeScreenshot('report_type_error');
            this.log(`Failed to select report type: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * تقديم البلاغ
     */
    async submitReport() {
        try {
            this.log('📤 Submitting report...');
            
            // البحث عن زر الإرسال
            await this.randomDelay('short');
            
            const submitButton = await this.page.evaluateHandle(() => {
                const buttons = Array.from(document.querySelectorAll('button'));
                return buttons.find(btn => 
                    btn.textContent.includes('Submit') ||
                    btn.textContent.includes('Send') ||
                    btn.textContent.includes('إرسال') ||
                    btn.textContent.includes('تقديم')
                );
            });
            
            if (!submitButton.asElement()) {
                throw new Error('Submit button not found');
            }
            
            await this.takeScreenshot('before_submit');
            await submitButton.asElement().click();
            
            await this.randomDelay('long');
            await this.takeScreenshot('after_submit');
            
            // التحقق من نجاح الإرسال
            const pageContent = await this.page.content();
            const success = pageContent.includes('Thank you') ||
                          pageContent.includes('شكراً') ||
                          pageContent.includes('Report sent') ||
                          pageContent.includes('تم إرسال البلاغ');
            
            if (success) {
                this.log('✅ Report submitted successfully', 'success');
                return true;
            } else {
                this.log('⚠️ Report submission unclear', 'warning');
                return false;
            }
            
        } catch (error) {
            await this.takeScreenshot('submit_error');
            this.log(`Failed to submit report: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * تنفيذ عملية الإبلاغ الكاملة
     */
    async executeReport(task) {
        const result = {
            task_id: task.task_id,
            success: false,
            details: {},
            screenshots: [],
            error: null
        };
        
        try {
            this.log(`🎯 Starting report execution for task: ${task.task_id}`);
            
            // بدء تشغيل المتصفح
            await this.launchBrowser();
            result.screenshots.push(await this.takeScreenshot('browser_started'));
            
            // تسجيل الدخول
            await this.loginToInstagram(task.credentials);
            result.screenshots.push(await this.takeScreenshot('logged_in'));
            
            // زيارة الحساب المستهدف
            await this.visitTargetProfile(task.target_account);
            result.screenshots.push(await this.takeScreenshot('target_visited'));
            
            // فتح قائمة الإبلاغ
            await this.openReportMenu();
            result.screenshots.push(await this.takeScreenshot('report_menu'));
            
            // اختيار نوع البلاغ
            await this.selectReportType(task.report_category);
            result.screenshots.push(await this.takeScreenshot('report_type'));
            
            // تقديم البلاغ
            const submitted = await this.submitReport();
            result.screenshots.push(await this.takeScreenshot('final_result'));
            
            result.success = submitted;
            result.details = {
                target_account: task.target_account,
                report_category: task.report_category,
                submitted_at: new Date().toISOString(),
                execution_time: Date.now() - task.start_time
            };
            
            this.log(`🎉 Report execution completed successfully`, 'success');
            
        } catch (error) {
            result.error = error.message;
            result.screenshots.push(await this.takeScreenshot('error_state'));
            this.log(`❌ Report execution failed: ${error.message}`, 'error');
        } finally {
            await this.cleanup();
        }
        
        return result;
    }

    /**
     * تنظيف الموارد
     */
    async cleanup() {
        try {
            if (this.browser) {
                await this.browser.close();
                this.log('🧹 Browser closed', 'debug');
            }
        } catch (error) {
            this.log(`Cleanup error: ${error.message}`, 'warning');
        }
    }
}

// تشغيل السكربت الرئيسي
async function main() {
    const reporter = new InstagramReporter();
    
    try {
        // قراءة مهمة الإبلاغ
        const task = await reporter.readReportTask();
        
        // تنفيذ الإبلاغ
        const result = await reporter.executeReport(task);
        
        // كتابة النتائج
        await reporter.writeReportLog(result);
        
        process.exit(result.success ? 0 : 1);
        
    } catch (error) {
        reporter.log(`Main execution failed: ${error.message}`, 'error');
        process.exit(1);
    }
}

// تشغيل السكربت إذا تم استدعاؤه مباشرة
if (require.main === module) {
    main();
}

module.exports = InstagramReporter;
