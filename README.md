# 🚀 Enhanced Social Media Blocking System
## نظام حجب وسائل التواصل الاجتماعي المطور

A comprehensive Python module with **real web automation capabilities** using Puppeteer for social media blocking and reporting mechanisms across multiple platforms.

## ✨ New Features (Enhanced Version)

- 🎭 **Real Web Automation**: Uses Puppeteer for actual browser control
- 🔄 **Dual Mode Operation**: Switch between real automation and safe simulation
- 🕷️ **Account Monitoring**: Scrapy-based monitoring system
- 📋 **Advanced Task Management**: JSON-based task queue system
- 🛡️ **Safety First**: Safe testing mode for educational purposes
- 📸 **Screenshot Capture**: Visual documentation of automation steps
- ⚡ **Smart Delays**: Random delays to avoid detection
- 🔧 **Modular Architecture**: Separate components for different functions

## 🎯 Core Features

- **Multi-platform Support**: Instagram, Facebook, Twitter, YouTube, TikTok, LinkedIn
- **Mass Reporting Engine**: Real and simulated coordinated reporting campaigns
- **Content Violation Detection**: Identify potentially harmful content
- **Copyright Strike System**: Simulate DMCA takedown processes
- **Impersonation Detection**: Detect fake accounts and profiles
- **Coordinated Campaign Management**: Organize blocking efforts
- **AI-powered Content Analysis**: Advanced content classification (when NumPy available)
- **Database Integration**: SQLite database for persistent storage
- **Comprehensive Logging**: Detailed activity tracking
- **Flexible Configuration**: Customizable blocking strategies

## 🛠️ Installation

### 1. Python Dependencies
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install Python packages
pip install scrapy
```

### 2. Node.js Dependencies
```bash
# Install Node.js dependencies
npm install

# Verify installation
node --version
npm --version
```

### 3. Optional Dependencies
```bash
pip install requests numpy  # For enhanced features
```

## 🚀 Quick Start

### Safe Mode (Simulation)
```python
from social_blocking_standalone import StandaloneSocialMediaBlocking

# Initialize in safe simulation mode
blocker = StandaloneSocialMediaBlocking(use_real_automation=False)
blocker.start_blocking_system()

# Execute a simulated campaign
campaign_config = {
    'target_account': 'test_account',
    'platform': 'instagram',
    'report_category': 'spam',
    'reporter_count': 5
}

campaign_id = blocker.execute_mass_reporting_campaign(campaign_config)
print(f"✅ Simulated campaign {campaign_id} completed")
```

### Real Automation Mode (Advanced)
```python
# Initialize with real automation (requires real accounts)
blocker = StandaloneSocialMediaBlocking(use_real_automation=True)
blocker.start_blocking_system()

# Execute real campaign (use responsibly!)
campaign_config = {
    'target_account': 'target_account',
    'platform': 'instagram',
    'report_category': 'spam',
    'reporter_count': 3
}

campaign_id = blocker.execute_mass_reporting_campaign(campaign_config)
print(f"🎯 Real campaign {campaign_id} executed")
```

## 🧪 Testing

### Run Complete Test Suite
```bash
# Activate virtual environment
source venv/bin/activate

# Run enhanced system tests
python test_enhanced_system.py
```

### Test Individual Components
```bash
# Test task manager
python task_manager.py

# Test account monitor
python account_monitor.py

# Test original system
python test_social_blocking.py
```

## 📁 Project Structure

```
social_media_blocking/
├── 📄 social_blocking_standalone.py    # Enhanced main system
├── 🎭 puppeteer_reporter.js           # Puppeteer automation script
├── 📋 task_manager.py                 # Task management system
├── 🕷️ account_monitor.py              # Scrapy monitoring spider
├── 🧪 test_enhanced_system.py         # Comprehensive test suite
├── 📦 package.json                    # Node.js dependencies
├── 📊 enhanced_system_test_report.json # Test results
├── 📚 ENHANCED_SYSTEM_GUIDE.md        # Detailed usage guide
├── 📚 SOCIAL_BLOCKING_GUIDE.md        # Original guide
├── 🗃️ results/                        # Results directory
├── 📸 screenshots/                    # Screenshots directory
└── 🗄️ social_media_blocking.db        # SQLite database
```

## 🎮 Usage Examples

### Switch Between Modes
```python
# Check current mode
mode_info = blocker.get_automation_mode()
print(f"Current mode: {mode_info['mode_name']}")

# Switch to real automation
blocker.set_automation_mode(True)

# Switch back to simulation
blocker.set_automation_mode(False)
```

### Direct Puppeteer Usage
```python
from task_manager import TaskManager

task_manager = TaskManager()

# Create and execute a single task
task_id = task_manager.create_report_task(
    target_account="target_user",
    report_category="harassment",
    reporter_account_id="reporter_1"
)

result = task_manager.execute_puppeteer_task(task_id)
print(f"Success: {result['success']}")
```

### Account Monitoring
```python
from account_monitor import AccountMonitorManager

monitor = AccountMonitorManager()

# Create monitoring list from campaign results
campaign_results = [
    {
        'target_account': 'monitored_account',
        'platform': 'instagram',
        'campaign_id': 'campaign_123',
        'success': True
    }
]

monitor.create_monitoring_list(campaign_results)
monitor.run_monitoring()

# Check results
results = monitor.get_monitoring_results()
print(f"Monitored accounts: {results['total_accounts']}")
```

## ⚠️ Important Warnings

### 🔴 Educational Use Only
- This system is designed for **educational and research purposes only**
- Do not use against real accounts without explicit permission
- Respect platform terms of service and legal requirements
- Use the simulation mode for safe testing

### 🛡️ Safety Guidelines
- Always start with simulation mode
- Use real automation responsibly and ethically
- Implement appropriate delays to avoid detection
- Monitor resource usage and clean up temporary files
- Use VPN when necessary for privacy

### 📋 Legal Disclaimer
Users are responsible for ensuring their use of this system complies with:
- Local laws and regulations
- Platform terms of service
- Ethical guidelines for automated systems
- Privacy and data protection requirements

## 🔧 Configuration

### Puppeteer Settings
Edit `puppeteer_reporter.js` to customize:
- Browser launch options
- Delay configurations
- Screenshot settings
- User agent strings

### Scrapy Settings
Modify `account_monitor.py` for:
- Request delays
- Concurrent request limits
- User agent rotation
- Proxy configuration

## 📊 Monitoring and Results

### Result Files
- `report_log.json` - Puppeteer execution results
- `status_log.json` - Account monitoring results
- `results/` - Detailed campaign results
- `screenshots/` - Visual documentation

### Performance Metrics
The system tracks:
- Success rates
- Execution times
- Error frequencies
- Resource usage
- Detection avoidance metrics

## 🆘 Troubleshooting

### Common Issues
1. **Puppeteer fails to launch**: Check Node.js installation and dependencies
2. **Scrapy import errors**: Ensure virtual environment is activated
3. **Permission errors**: Check file permissions and directory access
4. **Network timeouts**: Adjust delay settings and check connectivity

### Debug Mode
```bash
# Enable debug logging
DEBUG=puppeteer:* node puppeteer_reporter.js

# Verbose Scrapy output
scrapy crawl account_monitor -L DEBUG
```

## 🤝 Contributing

This is an educational project. Contributions should focus on:
- Improving safety features
- Adding educational documentation
- Enhancing testing capabilities
- Fixing bugs and security issues

## 📄 License

This project is for educational purposes only. Users must comply with all applicable laws and platform terms of service.

---

**🎯 Ready to explore? Start with the simulation mode and check out the [Enhanced System Guide](ENHANCED_SYSTEM_GUIDE.md) for detailed instructions!**
