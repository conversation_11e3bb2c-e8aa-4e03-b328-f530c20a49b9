#!/usr/bin/env python3
"""
Enhanced System Test Suite - اختبار النظام المطور
اختبار شامل للنظام المحدث مع دعم Puppeteer والمراقبة
"""

import sys
import time
import json
import os
from datetime import datetime

# استيراد النظام المطور
from social_blocking_standalone import StandaloneSocialMediaBlocking
from task_manager import TaskManager
from account_monitor import AccountMonitorManager

class EnhancedSystemTester:
    def __init__(self):
        self.test_results = []
        self.start_time = time.time()
        
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 ENHANCED SOCIAL MEDIA BLOCKING SYSTEM TEST")
        print("=" * 70)
        
        # Test 1: نظام التحكم في الوضع
        print("\n🔧 Test 1: Mode Control System")
        self.test_mode_control()
        
        # Test 2: Task Manager
        print("\n📋 Test 2: Task Manager")
        self.test_task_manager()
        
        # Test 3: Enhanced Mass Reporting Engine
        print("\n📢 Test 3: Enhanced Mass Reporting Engine")
        self.test_enhanced_mass_reporting()
        
        # Test 4: Account Monitor
        print("\n🕷️ Test 4: Account Monitor")
        self.test_account_monitor()
        
        # Test 5: Integration Test
        print("\n🔗 Test 5: System Integration")
        self.test_system_integration()
        
        # Test 6: Puppeteer Files Check
        print("\n🎭 Test 6: Puppeteer Files Check")
        self.test_puppeteer_files()
        
        # Generate final report
        self.generate_final_report()

    def test_mode_control(self):
        """اختبار نظام التحكم في الوضع"""
        try:
            print("[*] Testing mode control system...")
            
            # اختبار الوضع الوهمي
            social_blocking_fake = StandaloneSocialMediaBlocking(use_real_automation=False)
            mode_info = social_blocking_fake.get_automation_mode()
            
            assert mode_info['use_real_automation'] == False
            assert 'FAKE SIMULATION' in mode_info['mode_name']
            print("    [✓] Fake mode initialization: PASSED")
            
            # اختبار الوضع الحقيقي
            social_blocking_real = StandaloneSocialMediaBlocking(use_real_automation=True)
            mode_info = social_blocking_real.get_automation_mode()
            
            assert mode_info['use_real_automation'] == True
            assert 'REAL AUTOMATION' in mode_info['mode_name']
            print("    [✓] Real mode initialization: PASSED")
            
            # اختبار تغيير الوضع
            social_blocking_fake.set_automation_mode(True)
            mode_info = social_blocking_fake.get_automation_mode()
            assert mode_info['use_real_automation'] == True
            print("    [✓] Mode switching: PASSED")
            
            self.test_results.append({
                'test': 'mode_control',
                'status': 'passed',
                'details': 'All mode control tests passed'
            })
            
        except Exception as e:
            print(f"    [✗] Mode control test failed: {e}")
            self.test_results.append({
                'test': 'mode_control',
                'status': 'failed',
                'details': str(e)
            })

    def test_task_manager(self):
        """اختبار مدير المهام"""
        try:
            print("[*] Testing task manager...")
            
            # إنشاء مدير المهام
            task_manager = TaskManager()
            
            # اختبار إنشاء مهمة
            task_id = task_manager.create_report_task(
                target_account="test_target",
                report_category="spam",
                reporter_account_id="test_reporter"
            )
            
            assert task_id is not None
            assert task_id.startswith("task_")
            print("    [✓] Task creation: PASSED")
            
            # اختبار وجود ملف المهمة
            assert os.path.exists(task_manager.task_file)
            print("    [✓] Task file creation: PASSED")
            
            # اختبار قراءة المهمة
            with open(task_manager.task_file, 'r') as f:
                task_data = json.load(f)
            
            assert task_data['task_id'] == task_id
            assert task_data['target_account'] == "test_target"
            print("    [✓] Task data validation: PASSED")
            
            # تنظيف
            task_manager.cleanup_task_files()
            
            self.test_results.append({
                'test': 'task_manager',
                'status': 'passed',
                'details': f'Task manager tests passed, created task: {task_id}'
            })
            
        except Exception as e:
            print(f"    [✗] Task manager test failed: {e}")
            self.test_results.append({
                'test': 'task_manager',
                'status': 'failed',
                'details': str(e)
            })

    def test_enhanced_mass_reporting(self):
        """اختبار محرك الإبلاغ الجماعي المطور"""
        try:
            print("[*] Testing enhanced mass reporting engine...")
            
            # اختبار الوضع الوهمي
            social_blocking = StandaloneSocialMediaBlocking(use_real_automation=False)
            social_blocking.start_blocking_system()
            
            config = {
                'target_account': 'test_target_enhanced',
                'platform': 'instagram',
                'report_category': 'spam',
                'reporter_count': 5
            }
            
            campaign_id = social_blocking.execute_mass_reporting_campaign(config)
            assert campaign_id is not None
            print("    [✓] Fake mode campaign: PASSED")
            
            # اختبار الوضع الحقيقي (بدون تنفيذ فعلي)
            social_blocking.set_automation_mode(True)
            
            # التحقق من تهيئة TaskManager
            engine = social_blocking.blocking_engines['mass_reporting_engine']
            assert hasattr(engine, 'use_real_automation')
            assert engine.use_real_automation == True
            print("    [✓] Real mode configuration: PASSED")
            
            social_blocking.stop_blocking_system()
            
            self.test_results.append({
                'test': 'enhanced_mass_reporting',
                'status': 'passed',
                'details': f'Enhanced mass reporting tests passed, campaign: {campaign_id}'
            })
            
        except Exception as e:
            print(f"    [✗] Enhanced mass reporting test failed: {e}")
            self.test_results.append({
                'test': 'enhanced_mass_reporting',
                'status': 'failed',
                'details': str(e)
            })

    def test_account_monitor(self):
        """اختبار مراقب الحسابات"""
        try:
            print("[*] Testing account monitor...")
            
            # إنشاء مراقب الحسابات
            monitor = AccountMonitorManager()
            
            # إنشاء قائمة اختبار
            test_results = [
                {
                    'target_account': 'test_account_1',
                    'platform': 'instagram',
                    'campaign_id': 'test_campaign_1',
                    'success': True,
                    'timestamp': datetime.now().isoformat()
                }
            ]
            
            # إنشاء قائمة المراقبة
            monitoring_list = monitor.create_monitoring_list(test_results)
            assert len(monitoring_list) == 1
            assert monitoring_list[0]['username'] == 'test_account_1'
            print("    [✓] Monitoring list creation: PASSED")
            
            # التحقق من وجود ملف المراقبة
            assert os.path.exists(monitor.accounts_file)
            print("    [✓] Monitoring file creation: PASSED")
            
            # تنظيف
            if os.path.exists(monitor.accounts_file):
                os.remove(monitor.accounts_file)
            
            self.test_results.append({
                'test': 'account_monitor',
                'status': 'passed',
                'details': 'Account monitor tests passed'
            })
            
        except Exception as e:
            print(f"    [✗] Account monitor test failed: {e}")
            self.test_results.append({
                'test': 'account_monitor',
                'status': 'failed',
                'details': str(e)
            })

    def test_system_integration(self):
        """اختبار تكامل النظام"""
        try:
            print("[*] Testing system integration...")
            
            # إنشاء النظام الكامل
            social_blocking = StandaloneSocialMediaBlocking(use_real_automation=False)
            task_manager = TaskManager()
            monitor = AccountMonitorManager()
            
            # اختبار التكامل
            social_blocking.start_blocking_system()
            
            # تنفيذ حملة وهمية
            config = {
                'target_account': 'integration_test_target',
                'platform': 'instagram',
                'report_category': 'spam',
                'reporter_count': 3
            }
            
            campaign_id = social_blocking.execute_mass_reporting_campaign(config)
            assert campaign_id is not None
            print("    [✓] Campaign execution: PASSED")
            
            # محاكاة نتائج للمراقبة
            fake_results = [
                {
                    'target_account': 'integration_test_target',
                    'platform': 'instagram',
                    'campaign_id': campaign_id,
                    'success': True,
                    'timestamp': datetime.now().isoformat()
                }
            ]
            
            # إنشاء قائمة مراقبة
            monitoring_list = monitor.create_monitoring_list(fake_results)
            assert len(monitoring_list) > 0
            print("    [✓] Monitoring integration: PASSED")
            
            # الحصول على حالة النظام
            status = social_blocking.get_blocking_system_status()
            assert 'automation_mode' in status
            assert status['active'] == True
            print("    [✓] System status: PASSED")
            
            social_blocking.stop_blocking_system()
            
            # تنظيف
            if os.path.exists(monitor.accounts_file):
                os.remove(monitor.accounts_file)
            
            self.test_results.append({
                'test': 'system_integration',
                'status': 'passed',
                'details': f'Integration tests passed, campaign: {campaign_id}'
            })
            
        except Exception as e:
            print(f"    [✗] System integration test failed: {e}")
            self.test_results.append({
                'test': 'system_integration',
                'status': 'failed',
                'details': str(e)
            })

    def test_puppeteer_files(self):
        """اختبار وجود ملفات Puppeteer"""
        try:
            print("[*] Testing Puppeteer files...")
            
            required_files = [
                'package.json',
                'puppeteer_reporter.js',
                'task_manager.py',
                'account_monitor.py'
            ]
            
            missing_files = []
            for file in required_files:
                if not os.path.exists(file):
                    missing_files.append(file)
                else:
                    print(f"    [✓] {file}: EXISTS")
            
            if missing_files:
                raise Exception(f"Missing files: {missing_files}")
            
            # التحقق من محتوى package.json
            with open('package.json', 'r') as f:
                package_data = json.load(f)
            
            required_deps = ['puppeteer', 'puppeteer-extra']
            for dep in required_deps:
                if dep not in package_data.get('dependencies', {}):
                    raise Exception(f"Missing dependency: {dep}")
                else:
                    print(f"    [✓] Dependency {dep}: FOUND")
            
            self.test_results.append({
                'test': 'puppeteer_files',
                'status': 'passed',
                'details': 'All required files and dependencies found'
            })
            
        except Exception as e:
            print(f"    [✗] Puppeteer files test failed: {e}")
            self.test_results.append({
                'test': 'puppeteer_files',
                'status': 'failed',
                'details': str(e)
            })

    def generate_final_report(self):
        """توليد التقرير النهائي"""
        print("\n" + "=" * 70)
        print("📊 ENHANCED SYSTEM TEST REPORT")
        print("=" * 70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['status'] == 'passed')
        failed_tests = total_tests - passed_tests
        
        execution_time = time.time() - self.start_time
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        print(f"Execution Time: {execution_time:.2f} seconds")
        
        print(f"\nSuccess Rate: {passed_tests/total_tests*100:.1f}%")
        
        print("\nDetailed Results:")
        for result in self.test_results:
            status_icon = "✓" if result['status'] == 'passed' else "✗"
            print(f"  [{status_icon}] {result['test']}: {result['details']}")
        
        # تقييم شامل
        if passed_tests / total_tests >= 0.9:
            print(f"\n🎉 OVERALL ASSESSMENT: EXCELLENT")
            print("The enhanced social media blocking system is working perfectly!")
        elif passed_tests / total_tests >= 0.7:
            print(f"\n👍 OVERALL ASSESSMENT: GOOD")
            print("The enhanced system is working well with minor issues.")
        else:
            print(f"\n⚠️ OVERALL ASSESSMENT: NEEDS IMPROVEMENT")
            print("The enhanced system needs attention.")
        
        # حفظ التقرير
        report = {
            'test_time': datetime.now().isoformat(),
            'execution_time': execution_time,
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': passed_tests/total_tests*100,
            'results': self.test_results
        }
        
        with open('enhanced_system_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Test report saved: enhanced_system_test_report.json")
        print("=" * 70)

def main():
    """دالة الاختبار الرئيسية"""
    print("🧪 Starting Enhanced Social Media Blocking System Tests...")
    
    try:
        tester = EnhancedSystemTester()
        tester.run_all_tests()
        
    except KeyboardInterrupt:
        print("\n[!] Tests interrupted by user")
    except Exception as e:
        print(f"\n[✗] Test suite error: {e}")
    finally:
        print("\n[*] Enhanced test suite completed")

if __name__ == "__main__":
    main()
