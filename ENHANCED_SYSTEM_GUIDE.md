# 🚀 Enhanced Social Media Blocking System Guide
## دليل النظام المطور لحجب وسائل التواصل الاجتماعي

### 📋 نظرة عامة
تم تطوير النظام ليصبح قادراً على التفاعل الحقيقي مع الويب باستخدام **Puppeteer** بدلاً من المحاكاة الوهمية فقط. النظام الآن يدعم:

- ✅ **التنفيذ الحقيقي** باستخدام Puppeteer لأتمتة المتصفح
- ✅ **المحاكاة الآمنة** للاختبارات والتطوير
- ✅ **مراقبة الحسابات** باستخدام Scrapy
- ✅ **إدارة المهام** المتقدمة مع JSON
- ✅ **التبديل السهل** بين الأوضاع

---

## 🛠️ متطلبات التثبيت

### 1. متطل<PERSON>ات Python
```bash
# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows

# تثبيت المكتبات المطلوبة
pip install scrapy
```

### 2. متطلبات Node.js
```bash
# تثبيت التبعيات
npm install

# التحقق من التثبيت
node --version
npm --version
```

### 3. ملفات النظام المطلوبة
- ✅ `package.json` - إعدادات Node.js
- ✅ `puppeteer_reporter.js` - سكربت Puppeteer الرئيسي
- ✅ `task_manager.py` - مدير المهام
- ✅ `account_monitor.py` - مراقب الحسابات
- ✅ `social_blocking_standalone.py` - النظام الأساسي المطور

---

## 🎯 طرق الاستخدام

### 1. الوضع الوهمي (آمن للاختبار)
```python
from social_blocking_standalone import StandaloneSocialMediaBlocking

# إنشاء النظام في الوضع الوهمي
system = StandaloneSocialMediaBlocking(use_real_automation=False)
system.start_blocking_system()

# تنفيذ حملة وهمية
config = {
    'target_account': 'test_account',
    'platform': 'instagram',
    'report_category': 'spam',
    'reporter_count': 5
}

campaign_id = system.execute_mass_reporting_campaign(config)
print(f"Campaign completed: {campaign_id}")
```

### 2. الوضع الحقيقي (Puppeteer)
```python
# إنشاء النظام في الوضع الحقيقي
system = StandaloneSocialMediaBlocking(use_real_automation=True)
system.start_blocking_system()

# تنفيذ حملة حقيقية (تحتاج حسابات حقيقية)
config = {
    'target_account': 'real_target_account',
    'platform': 'instagram',
    'report_category': 'spam',
    'reporter_count': 3
}

campaign_id = system.execute_mass_reporting_campaign(config)
```

### 3. التبديل بين الأوضاع
```python
# التحقق من الوضع الحالي
mode_info = system.get_automation_mode()
print(f"Current mode: {mode_info['mode_name']}")

# التبديل إلى الوضع الحقيقي
system.set_automation_mode(True)

# التبديل إلى الوضع الوهمي
system.set_automation_mode(False)
```

---

## 🎭 استخدام Puppeteer مباشرة

### 1. إنشاء مهمة يدوياً
```python
from task_manager import TaskManager

task_manager = TaskManager()

# إنشاء مهمة إبلاغ
task_id = task_manager.create_report_task(
    target_account="target_username",
    report_category="spam",
    reporter_account_id="reporter_1"
)

print(f"Task created: {task_id}")
```

### 2. تنفيذ مهمة Puppeteer
```python
# تنفيذ المهمة
result = task_manager.execute_puppeteer_task(task_id)

if result['success']:
    print("✅ Report submitted successfully!")
    print(f"Screenshots: {result.get('screenshots', [])}")
else:
    print(f"❌ Report failed: {result.get('error', 'Unknown error')}")
```

### 3. تنفيذ حملة متعددة الحسابات
```python
# تنفيذ حملة كاملة
results = task_manager.execute_report_campaign(
    target_account="target_username",
    report_category="harassment",
    reporter_accounts=["reporter_1", "reporter_2", "reporter_3"],
    delay_between_reports=60  # 60 ثانية بين كل تقرير
)

# عرض النتائج
for result in results:
    status = "✅" if result.get('success') else "❌"
    print(f"{status} {result.get('task_id', 'Unknown')}")
```

---

## 🕷️ مراقبة الحسابات باستخدام Scrapy

### 1. إنشاء قائمة مراقبة
```python
from account_monitor import AccountMonitorManager

monitor = AccountMonitorManager()

# إنشاء قائمة من نتائج الحملة
campaign_results = [
    {
        'target_account': 'monitored_account',
        'platform': 'instagram',
        'campaign_id': 'campaign_123',
        'success': True,
        'timestamp': '2024-01-01T12:00:00'
    }
]

monitoring_list = monitor.create_monitoring_list(campaign_results)
```

### 2. تشغيل المراقبة
```python
# تشغيل مراقبة الحسابات
monitor.run_monitoring()

# قراءة النتائج
results = monitor.get_monitoring_results()
if results:
    print(f"Total accounts monitored: {results['total_accounts']}")
    print(f"Status summary: {results['status_summary']}")
```

---

## ⚙️ الإعدادات المتقدمة

### 1. إعدادات Puppeteer
يمكن تعديل إعدادات Puppeteer في `puppeteer_reporter.js`:

```javascript
// إعدادات التأخير
this.delays = {
    short: [1000, 3000],    // 1-3 ثواني
    medium: [3000, 7000],   // 3-7 ثواني
    long: [5000, 12000]     // 5-12 ثانية
};

// إعدادات المتصفح
this.browser = await puppeteer.launch({
    headless: false,        // إظهار المتصفح
    defaultViewport: null,
    args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--window-size=1366,768'
    ]
});
```

### 2. إعدادات Scrapy
تعديل إعدادات المراقبة في `account_monitor.py`:

```python
# إعدادات Scrapy
settings = {
    'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'ROBOTSTXT_OBEY': False,
    'DOWNLOAD_DELAY': 2,
    'RANDOMIZE_DOWNLOAD_DELAY': True,
    'CONCURRENT_REQUESTS': 1
}
```

---

## 🧪 تشغيل الاختبارات

### 1. اختبار النظام الكامل
```bash
# تفعيل البيئة الافتراضية
source venv/bin/activate

# تشغيل الاختبارات الشاملة
python test_enhanced_system.py
```

### 2. اختبار مكونات منفردة
```python
# اختبار Task Manager
python task_manager.py

# اختبار Account Monitor
python account_monitor.py

# اختبار النظام الأساسي
python test_social_blocking.py
```

---

## 📊 مراقبة النتائج

### 1. ملفات النتائج
- `report_log.json` - نتائج Puppeteer
- `status_log.json` - نتائج مراقبة الحسابات
- `results/` - مجلد النتائج المفصلة
- `screenshots/` - لقطات الشاشة

### 2. قراءة النتائج
```python
import json

# قراءة نتائج Puppeteer
with open('report_log.json', 'r') as f:
    puppeteer_results = json.load(f)

# قراءة نتائج المراقبة
with open('status_log.json', 'r') as f:
    monitoring_results = json.load(f)

print(f"Report success: {puppeteer_results.get('success', False)}")
print(f"Account status: {monitoring_results.get('status_summary', {})}")
```

---

## ⚠️ تحذيرات مهمة

### 1. الاستخدام الأخلاقي
- 🔴 **للأغراض التعليمية فقط**
- 🔴 **لا تستخدم ضد حسابات حقيقية بدون إذن**
- 🔴 **احترم شروط الخدمة للمنصات**

### 2. الأمان
- 🛡️ استخدم الوضع الوهمي للاختبارات
- 🛡️ لا تشارك بيانات الاعتماد الحقيقية
- 🛡️ استخدم VPN عند الضرورة

### 3. الأداء
- ⚡ استخدم تأخيرات مناسبة لتجنب الحظر
- ⚡ راقب استهلاك الموارد
- ⚡ نظف الملفات المؤقتة بانتظام

---

## 🔧 استكشاف الأخطاء

### 1. مشاكل Puppeteer
```bash
# التحقق من تثبيت Node.js
node --version

# إعادة تثبيت التبعيات
npm install

# تشغيل في وضع debug
DEBUG=puppeteer:* node puppeteer_reporter.js
```

### 2. مشاكل Scrapy
```bash
# التحقق من تثبيت Scrapy
scrapy version

# تشغيل في وضع verbose
scrapy crawl account_monitor -L DEBUG
```

### 3. مشاكل عامة
```python
# التحقق من حالة النظام
system = StandaloneSocialMediaBlocking()
status = system.get_blocking_system_status()
print(json.dumps(status, indent=2))
```

---

## 📞 الدعم والمساعدة

للحصول على المساعدة:
1. راجع ملفات اللوج في مجلد `results/`
2. تحقق من ملف `enhanced_system_test_report.json`
3. استخدم الوضع الوهمي للاختبار أولاً
4. تأكد من تثبيت جميع التبعيات بشكل صحيح

---

**🎯 النظام جاهز للاستخدام! استمتع بالتجربة التعليمية الآمنة.**
