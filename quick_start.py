#!/usr/bin/env python3
"""
Quick Start Script - سكربت البدء السريع
تشغيل سريع للنظام المطور مع خيارات متعددة
"""

import sys
import json
import time
from datetime import datetime

def print_banner():
    """طباعة شعار النظام"""
    print("🚀" + "=" * 68 + "🚀")
    print("🎯  ENHANCED SOCIAL MEDIA BLOCKING SYSTEM - QUICK START  🎯")
    print("🚀" + "=" * 68 + "🚀")
    print()

def print_menu():
    """طباعة القائمة الرئيسية"""
    print("📋 Choose an option:")
    print("   1️⃣  Run Simulation Mode (Safe Testing)")
    print("   2️⃣  Run Real Automation Mode (Advanced)")
    print("   3️⃣  Test Task Manager")
    print("   4️⃣  Test Account Monitor")
    print("   5️⃣  Run Complete System Test")
    print("   6️⃣  Check System Status")
    print("   7️⃣  View Recent Results")
    print("   0️⃣  Exit")
    print()

def run_simulation_mode():
    """تشغيل الوضع الوهمي"""
    print("🛡️ Starting Simulation Mode...")
    print("=" * 50)
    
    try:
        from social_blocking_standalone import StandaloneSocialMediaBlocking
        
        # إنشاء النظام في الوضع الوهمي
        system = StandaloneSocialMediaBlocking(use_real_automation=False)
        system.start_blocking_system()
        
        print("✅ System started in simulation mode")
        
        # إعداد حملة تجريبية
        config = {
            'target_account': 'simulation_test_account',
            'platform': 'instagram',
            'report_category': 'spam',
            'reporter_count': 5
        }
        
        print(f"🎯 Executing simulation campaign...")
        print(f"   Target: {config['target_account']}")
        print(f"   Platform: {config['platform']}")
        print(f"   Category: {config['report_category']}")
        print(f"   Reporters: {config['reporter_count']}")
        
        campaign_id = system.execute_mass_reporting_campaign(config)
        
        if campaign_id:
            print(f"✅ Simulation campaign completed: {campaign_id}")
            
            # عرض الإحصائيات
            stats = system.get_blocking_system_status()
            print(f"📊 Campaign Statistics:")
            print(f"   Total campaigns: {stats['statistics']['total_campaigns']}")
            print(f"   Success rate: {stats['statistics']['success_rate']:.1f}%")
        else:
            print("❌ Simulation campaign failed")
        
        system.stop_blocking_system()
        print("🛑 System stopped")
        
    except Exception as e:
        print(f"❌ Error in simulation mode: {e}")

def run_real_automation_mode():
    """تشغيل الوضع الحقيقي"""
    print("⚠️  REAL AUTOMATION MODE WARNING ⚠️")
    print("=" * 50)
    print("🔴 This mode uses real web automation!")
    print("🔴 Only use for educational purposes!")
    print("🔴 Ensure you have permission for target accounts!")
    print()
    
    confirm = input("Type 'I UNDERSTAND' to continue: ")
    if confirm != 'I UNDERSTAND':
        print("❌ Real automation mode cancelled")
        return
    
    try:
        from social_blocking_standalone import StandaloneSocialMediaBlocking
        
        # إنشاء النظام في الوضع الحقيقي
        system = StandaloneSocialMediaBlocking(use_real_automation=True)
        system.start_blocking_system()
        
        print("✅ System started in real automation mode")
        
        # طلب بيانات الحملة من المستخدم
        print("\n📝 Enter campaign details:")
        target_account = input("Target account username: ").strip()
        if not target_account:
            print("❌ Target account is required")
            return
        
        platform = input("Platform (instagram/facebook/twitter) [instagram]: ").strip() or "instagram"
        report_category = input("Report category (spam/harassment/hate_speech) [spam]: ").strip() or "spam"
        reporter_count = input("Number of reporters [3]: ").strip() or "3"
        
        try:
            reporter_count = int(reporter_count)
            if reporter_count < 1 or reporter_count > 10:
                print("❌ Reporter count must be between 1 and 10")
                return
        except ValueError:
            print("❌ Invalid reporter count")
            return
        
        config = {
            'target_account': target_account,
            'platform': platform,
            'report_category': report_category,
            'reporter_count': reporter_count
        }
        
        print(f"\n🎯 Executing REAL campaign...")
        print(f"   Target: {config['target_account']}")
        print(f"   Platform: {config['platform']}")
        print(f"   Category: {config['report_category']}")
        print(f"   Reporters: {config['reporter_count']}")
        print("\n⏳ This may take several minutes...")
        
        campaign_id = system.execute_mass_reporting_campaign(config)
        
        if campaign_id:
            print(f"✅ Real campaign completed: {campaign_id}")
        else:
            print("❌ Real campaign failed")
        
        system.stop_blocking_system()
        print("🛑 System stopped")
        
    except Exception as e:
        print(f"❌ Error in real automation mode: {e}")

def test_task_manager():
    """اختبار مدير المهام"""
    print("📋 Testing Task Manager...")
    print("=" * 50)
    
    try:
        from task_manager import TaskManager
        
        task_manager = TaskManager()
        
        # إنشاء مهمة اختبار
        print("🔧 Creating test task...")
        task_id = task_manager.create_report_task(
            target_account="test_target_account",
            report_category="spam",
            reporter_account_id="test_reporter"
        )
        
        print(f"✅ Task created: {task_id}")
        
        # عرض معلومات المهمة
        if hasattr(task_manager, 'task_file'):
            import os
            if os.path.exists(task_manager.task_file):
                with open(task_manager.task_file, 'r') as f:
                    task_data = json.load(f)
                
                print("📄 Task Details:")
                print(f"   Task ID: {task_data.get('task_id', 'N/A')}")
                print(f"   Target: {task_data.get('target_account', 'N/A')}")
                print(f"   Category: {task_data.get('report_category', 'N/A')}")
                print(f"   Created: {task_data.get('created_at', 'N/A')}")
        
        # تنظيف
        task_manager.cleanup_task_files()
        print("🧹 Cleanup completed")
        
    except Exception as e:
        print(f"❌ Error testing task manager: {e}")

def test_account_monitor():
    """اختبار مراقب الحسابات"""
    print("🕷️ Testing Account Monitor...")
    print("=" * 50)
    
    try:
        from account_monitor import AccountMonitorManager
        
        monitor = AccountMonitorManager()
        
        # إنشاء قائمة اختبار
        print("📝 Creating test monitoring list...")
        test_results = [
            {
                'target_account': 'test_account_1',
                'platform': 'instagram',
                'campaign_id': 'test_campaign_1',
                'success': True,
                'timestamp': datetime.now().isoformat()
            },
            {
                'target_account': 'test_account_2',
                'platform': 'instagram',
                'campaign_id': 'test_campaign_2',
                'success': True,
                'timestamp': datetime.now().isoformat()
            }
        ]
        
        monitoring_list = monitor.create_monitoring_list(test_results)
        print(f"✅ Monitoring list created with {len(monitoring_list)} accounts")
        
        # عرض القائمة
        print("📋 Monitoring List:")
        for i, account in enumerate(monitoring_list, 1):
            print(f"   {i}. {account['username']} on {account['platform']}")
        
        # تنظيف
        import os
        if os.path.exists(monitor.accounts_file):
            os.remove(monitor.accounts_file)
        print("🧹 Cleanup completed")
        
    except Exception as e:
        print(f"❌ Error testing account monitor: {e}")

def run_complete_test():
    """تشغيل الاختبار الشامل"""
    print("🧪 Running Complete System Test...")
    print("=" * 50)
    
    try:
        import subprocess
        import os
        
        # تشغيل الاختبار الشامل
        result = subprocess.run(
            ['python', 'test_enhanced_system.py'],
            capture_output=True,
            text=True,
            timeout=120
        )
        
        print("📊 Test Results:")
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("⚠️ Warnings/Errors:")
            print(result.stderr)
        
        # قراءة تقرير الاختبار
        if os.path.exists('enhanced_system_test_report.json'):
            with open('enhanced_system_test_report.json', 'r') as f:
                report = json.load(f)
            
            print(f"\n📈 Summary:")
            print(f"   Total Tests: {report['total_tests']}")
            print(f"   Passed: {report['passed_tests']}")
            print(f"   Failed: {report['failed_tests']}")
            print(f"   Success Rate: {report['success_rate']:.1f}%")
        
    except subprocess.TimeoutExpired:
        print("⏰ Test timed out after 2 minutes")
    except Exception as e:
        print(f"❌ Error running complete test: {e}")

def check_system_status():
    """فحص حالة النظام"""
    print("🔍 Checking System Status...")
    print("=" * 50)
    
    try:
        from social_blocking_standalone import StandaloneSocialMediaBlocking
        
        # فحص النظام
        system = StandaloneSocialMediaBlocking()
        status = system.get_blocking_system_status()
        
        print("📊 System Status:")
        print(f"   Active: {status['active']}")
        print(f"   Mode: {status['automation_mode']['mode_name']}")
        print(f"   Platforms: {len(status['platforms'])}")
        print(f"   Capabilities: {sum(status['capabilities'].values())}/{len(status['capabilities'])}")
        
        print(f"\n📈 Statistics:")
        stats = status['statistics']
        print(f"   Total Campaigns: {stats['total_campaigns']}")
        print(f"   Successful Campaigns: {stats['successful_campaigns']}")
        print(f"   Success Rate: {stats['success_rate']:.1f}%")
        
        print(f"\n🔧 Libraries:")
        libs = status['libraries_available']
        for lib, available in libs.items():
            status_icon = "✅" if available else "❌"
            print(f"   {status_icon} {lib}")
        
        # فحص الملفات المطلوبة
        import os
        required_files = [
            'package.json',
            'puppeteer_reporter.js',
            'task_manager.py',
            'account_monitor.py'
        ]
        
        print(f"\n📁 Required Files:")
        for file in required_files:
            exists = os.path.exists(file)
            status_icon = "✅" if exists else "❌"
            print(f"   {status_icon} {file}")
        
    except Exception as e:
        print(f"❌ Error checking system status: {e}")

def view_recent_results():
    """عرض النتائج الحديثة"""
    print("📄 Viewing Recent Results...")
    print("=" * 50)
    
    import os
    import glob
    
    # البحث عن ملفات النتائج
    result_files = []
    
    # ملفات JSON
    json_files = glob.glob("*.json")
    for file in json_files:
        if any(keyword in file for keyword in ['result', 'log', 'report', 'status']):
            result_files.append(file)
    
    # مجلد النتائج
    if os.path.exists('results'):
        results_dir_files = glob.glob("results/*.json")
        result_files.extend(results_dir_files)
    
    if not result_files:
        print("📭 No recent results found")
        return
    
    print(f"📋 Found {len(result_files)} result files:")
    
    for i, file in enumerate(result_files, 1):
        try:
            stat = os.stat(file)
            size = stat.st_size
            mtime = datetime.fromtimestamp(stat.st_mtime)
            
            print(f"   {i}. {file}")
            print(f"      Size: {size} bytes")
            print(f"      Modified: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # عرض محتوى مختصر
            if file.endswith('.json') and size < 10000:  # أقل من 10KB
                try:
                    with open(file, 'r') as f:
                        data = json.load(f)
                    
                    if isinstance(data, dict):
                        if 'success_rate' in data:
                            print(f"      Success Rate: {data['success_rate']:.1f}%")
                        if 'total_tests' in data:
                            print(f"      Total Tests: {data['total_tests']}")
                        if 'campaign_id' in data:
                            print(f"      Campaign ID: {data['campaign_id']}")
                
                except:
                    pass
            
            print()
            
        except Exception as e:
            print(f"   {i}. {file} (Error reading: {e})")

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    while True:
        print_menu()
        
        try:
            choice = input("Enter your choice (0-7): ").strip()
            
            if choice == '0':
                print("👋 Goodbye!")
                break
            elif choice == '1':
                run_simulation_mode()
            elif choice == '2':
                run_real_automation_mode()
            elif choice == '3':
                test_task_manager()
            elif choice == '4':
                test_account_monitor()
            elif choice == '5':
                run_complete_test()
            elif choice == '6':
                check_system_status()
            elif choice == '7':
                view_recent_results()
            else:
                print("❌ Invalid choice. Please try again.")
            
            print("\n" + "─" * 50)
            input("Press Enter to continue...")
            print()
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            print("\n" + "─" * 50)
            input("Press Enter to continue...")

if __name__ == "__main__":
    main()
